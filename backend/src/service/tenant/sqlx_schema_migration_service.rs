use anyhow::Result;
use sqlx::{PgPool, Transaction, Postgres};
use sqlx::migrate::Migrator;
use tracing::{info, warn, error};
use std::path::Path;
use std::collections::HashMap;
use uuid::Uuid;

/// 基于 SQLx Migrator 的租户 Schema 迁移服务
pub struct SqlxSchemaMigrationService {
    pool: PgPool,
    tenant_migrator: Migrator,
}

impl SqlxSchemaMigrationService {
    /// 创建新的迁移服务实例
    pub async fn new(pool: PgPool, tenant_migrations_path: &str) -> Result<Self> {
        // 创建租户迁移器
        let tenant_migrator = Migrator::new(Path::new(tenant_migrations_path)).await?;
        
        Ok(Self {
            pool,
            tenant_migrator,
        })
    }

    /// 为所有租户执行迁移
    pub async fn migrate_all_tenants(&self) -> Result<()> {
        info!("🚀 Starting migration for all tenant schemas");
        
        let tenant_schemas = self.get_all_tenant_schemas().await?;
        let mut success_count = 0;
        let mut error_count = 0;
        
        for schema_name in tenant_schemas {
            match self.migrate_tenant_schema(&schema_name).await {
                Ok(_) => {
                    success_count += 1;
                    info!("✅ Successfully migrated schema: {}", schema_name);
                }
                Err(e) => {
                    error_count += 1;
                    error!("❌ Failed to migrate schema {}: {}", schema_name, e);
                }
            }
        }
        
        info!("📊 Migration completed: {} success, {} errors", success_count, error_count);
        
        if error_count > 0 {
            warn!("Some tenant schemas failed to migrate");
        }
        
        Ok(())
    }

    /// 为特定租户执行迁移
    pub async fn migrate_tenant_schema(&self, schema_name: &str) -> Result<()> {
        info!("🔄 Migrating tenant schema: {}", schema_name);
        
        // 验证 schema 名称
        self.validate_schema_name(schema_name)?;
        
        // 确保 schema 存在
        self.ensure_schema_exists(schema_name).await?;
        
        // 创建专用于该租户的连接池配置
        let tenant_pool = self.create_tenant_pool(schema_name).await?;
        
        // 执行迁移
        self.tenant_migrator.run(&tenant_pool).await?;
        
        info!("✅ Migration completed for schema: {}", schema_name);
        Ok(())
    }

    /// 获取租户迁移状态
    pub async fn get_migration_status(&self, schema_name: &str) -> Result<MigrationStatus> {
        let tenant_pool = self.create_tenant_pool(schema_name).await?;
        
        // 获取已应用的迁移
        let applied_migrations = sqlx::query!(
            r#"
            SELECT version, description, installed_on, checksum, execution_time
            FROM _sqlx_migrations 
            ORDER BY version
            "#
        )
        .fetch_all(&tenant_pool)
        .await?;
        
        // 获取待应用的迁移
        let available_migrations = self.tenant_migrator.iter().collect::<Vec<_>>();
        let applied_versions: std::collections::HashSet<_> = applied_migrations
            .iter()
            .map(|m| m.version)
            .collect();
        
        let pending_migrations: Vec<_> = available_migrations
            .iter()
            .filter(|m| !applied_versions.contains(&m.version))
            .map(|m| PendingMigration {
                version: m.version,
                description: m.description.clone(),
                checksum: format!("{:x}", m.checksum),
            })
            .collect();
        
        Ok(MigrationStatus {
            schema_name: schema_name.to_string(),
            applied_count: applied_migrations.len(),
            pending_count: pending_migrations.len(),
            applied_migrations: applied_migrations.into_iter().map(|m| AppliedMigration {
                version: m.version,
                description: m.description,
                installed_on: m.installed_on,
                execution_time: m.execution_time,
            }).collect(),
            pending_migrations,
        })
    }

    /// 创建租户专用的连接池
    async fn create_tenant_pool(&self, schema_name: &str) -> Result<PgPool> {
        // 获取当前连接的数据库 URL
        let database_url = std::env::var("DATABASE_URL")?;
        
        // 创建新的连接池，设置默认 search_path
        let pool = sqlx::postgres::PgPoolOptions::new()
            .max_connections(5)
            .after_connect(move |conn, _meta| {
                let schema = schema_name.to_string();
                Box::pin(async move {
                    sqlx::query(&format!(r#"SET search_path TO "{}""#, schema))
                        .execute(conn)
                        .await?;
                    Ok(())
                })
            })
            .connect(&database_url)
            .await?;
        
        Ok(pool)
    }

    /// 确保 schema 存在
    async fn ensure_schema_exists(&self, schema_name: &str) -> Result<()> {
        let create_schema_sql = format!(r#"CREATE SCHEMA IF NOT EXISTS "{}""#, schema_name);
        sqlx::query(&create_schema_sql).execute(&self.pool).await?;
        Ok(())
    }

    /// 获取所有租户 schema
    async fn get_all_tenant_schemas(&self) -> Result<Vec<String>> {
        let schemas = sqlx::query_scalar!(
            r#"
            SELECT schema_name
            FROM public.tenants
            "#
        )
        .fetch_all(&self.pool)
        .await?;
        
        Ok(schemas)
    }

    /// 验证 schema 名称
    fn validate_schema_name(&self, schema_name: &str) -> Result<()> {
        if !schema_name.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(anyhow::anyhow!("Invalid schema name: {}", schema_name));
        }
        Ok(())
    }

    /// 回滚到指定版本
    pub async fn rollback_to_version(&self, schema_name: &str, target_version: i64) -> Result<()> {
        info!("⏪ Rolling back schema {} to version {}", schema_name, target_version);
        
        let tenant_pool = self.create_tenant_pool(schema_name).await?;
        
        // 获取需要回滚的迁移
        let migrations_to_revert = sqlx::query!(
            r#"
            SELECT version, description 
            FROM _sqlx_migrations 
            WHERE version > $1 
            ORDER BY version DESC
            "#,
            target_version
        )
        .fetch_all(&tenant_pool)
        .await?;
        
        if migrations_to_revert.is_empty() {
            info!("No migrations to rollback for schema: {}", schema_name);
            return Ok(());
        }
        
        // 注意：SQLx 不直接支持回滚，这里需要手动实现
        warn!("⚠️ Rollback requires manual intervention. Migrations to revert:");
        for migration in migrations_to_revert {
            warn!("  - Version {}: {}", migration.version, migration.description);
        }
        
        // 在实际项目中，你可能需要：
        // 1. 准备回滚脚本
        // 2. 手动执行回滚 SQL
        // 3. 更新迁移历史表
        
        Ok(())
    }
}

#[derive(Debug)]
pub struct MigrationStatus {
    pub schema_name: String,
    pub applied_count: usize,
    pub pending_count: usize,
    pub applied_migrations: Vec<AppliedMigration>,
    pub pending_migrations: Vec<PendingMigration>,
}

#[derive(Debug)]
pub struct AppliedMigration {
    pub version: i64,
    pub description: String,
    pub installed_on: chrono::DateTime<chrono::Utc>,
    pub execution_time: Option<i32>,
}

#[derive(Debug)]
pub struct PendingMigration {
    pub version: i64,
    pub description: String,
    pub checksum: String,
}

/// 便利函数：创建标准的租户迁移器
pub async fn create_tenant_migrator() -> Result<Migrator> {
    Migrator::new(Path::new("migrations_tenant")).await
}

/// 便利函数：为单个租户执行迁移
pub async fn migrate_single_tenant(pool: &PgPool, schema_name: &str) -> Result<()> {
    let service = SqlxSchemaMigrationService::new(pool.clone(), "migrations_tenant").await?;
    service.migrate_tenant_schema(schema_name).await
}
