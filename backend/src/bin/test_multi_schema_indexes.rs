use sqlx::PgPool;
use std::env;

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 从环境变量获取数据库连接
    let database_url = env::var("DATABASE_URL")
        .unwrap_or_else(|_| "postgresql://deep_mate:deep_mate@localhost:5432/deep_mate".to_string());
    
    let pool = PgPool::connect(&database_url).await?;
    
    println!("🔍 测试多 Schema 下的索引重名问题");
    
    // 创建两个测试 schema
    let schema1 = "test_schema_1";
    let schema2 = "test_schema_2";
    
    println!("📝 创建测试 schemas: {} 和 {}", schema1, schema2);
    
    // 创建 schemas
    sqlx::query(&format!("DROP SCHEMA IF EXISTS {} CASCADE", schema1))
        .execute(&pool)
        .await?;
        
    sqlx::query(&format!("DROP SCHEMA IF EXISTS {} CASCADE", schema2))
        .execute(&pool)
        .await?;
    
    sqlx::query(&format!("CREATE SCHEMA {}", schema1))
        .execute(&pool)
        .await?;
        
    sqlx::query(&format!("CREATE SCHEMA {}", schema2))
        .execute(&pool)
        .await?;
    
    // 在两个 schema 中创建相同的表和索引
    for schema in [schema1, schema2] {
        println!("🏗️  在 schema {} 中创建表和索引", schema);
        
        // 创建表
        sqlx::query(&format!(
            r#"
            CREATE TABLE {}.test_users (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                email VARCHAR(255) NOT NULL,
                name VARCHAR(100) NOT NULL
            )
            "#,
            schema
        ))
        .execute(&pool)
        .await?;
        
        // 创建相同名称的索引
        sqlx::query(&format!(
            "CREATE INDEX idx_users_email ON {}.test_users (email)",
            schema
        ))
        .execute(&pool)
        .await?;
        
        sqlx::query(&format!(
            "CREATE INDEX idx_users_name ON {}.test_users (name)",
            schema
        ))
        .execute(&pool)
        .await?;
        
        println!("✅ Schema {} 的表和索引创建成功", schema);
    }
    
    // 验证索引都存在且可以正常使用
    for schema in [schema1, schema2] {
        println!("🧪 测试 schema {} 的索引功能", schema);
        
        // 插入测试数据
        sqlx::query(&format!(
            "INSERT INTO {}.test_users (email, name) VALUES ($1, $2)",
            schema
        ))
        .bind(format!("test@{}.com", schema))
        .bind(format!("Test User {}", schema))
        .execute(&pool)
        .await?;
        
        // 使用索引查询
        let count: i64 = sqlx::query_scalar(&format!(
            "SELECT COUNT(*) FROM {}.test_users WHERE email = $1",
            schema
        ))
        .bind(format!("test@{}.com", schema))
        .fetch_one(&pool)
        .await?;
        
        assert_eq!(count, 1);
        println!("✅ Schema {} 的索引查询正常", schema);
    }
    
    // 验证索引信息
    println!("📊 查询索引信息");
    let indexes = sqlx::query!(
        r#"
        SELECT schemaname, indexname, tablename 
        FROM pg_indexes 
        WHERE indexname IN ('idx_users_email', 'idx_users_name')
        AND schemaname IN ($1, $2)
        ORDER BY schemaname, indexname
        "#,
        schema1,
        schema2
    )
    .fetch_all(&pool)
    .await?;
    
    // 应该有 4 个索引：每个 schema 2 个
    assert_eq!(indexes.len(), 4);
    
    // 验证索引分布
    let schema1_indexes = indexes.iter().filter(|i| i.schemaname == schema1).count();
    let schema2_indexes = indexes.iter().filter(|i| i.schemaname == schema2).count();
    
    assert_eq!(schema1_indexes, 2);
    assert_eq!(schema2_indexes, 2);
    
    println!("✅ 多 Schema 索引重名测试通过！");
    println!("📋 找到的索引:");
    for idx in indexes {
        println!("  - {}.{} on {}", idx.schemaname, idx.indexname, idx.tablename);
    }
    
    // 测试在同一 schema 内索引名称唯一性
    println!("🔒 测试同一 schema 内索引名称唯一性");
    
    // 创建另一个表
    sqlx::query(&format!(
        r#"
        CREATE TABLE {}.test_products (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name VARCHAR(255) NOT NULL
        )
        "#,
        schema1
    ))
    .execute(&pool)
    .await?;
    
    // 尝试创建相同名称的索引（应该失败）
    let result = sqlx::query(&format!(
        "CREATE INDEX idx_users_email ON {}.test_products (name)",
        schema1
    ))
    .execute(&pool)
    .await;
    
    // 验证索引名称冲突
    assert!(result.is_err());
    println!("✅ 同一 schema 内索引名称唯一性验证通过");
    
    // 清理
    println!("🧹 清理测试数据");
    sqlx::query(&format!("DROP SCHEMA {} CASCADE", schema1))
        .execute(&pool)
        .await?;
        
    sqlx::query(&format!("DROP SCHEMA {} CASCADE", schema2))
        .execute(&pool)
        .await?;
    
    println!("🎉 所有测试通过！");
    println!();
    println!("📝 结论:");
    println!("   ✅ PostgreSQL 中索引名称在 schema 级别唯一");
    println!("   ✅ 不同 schema 可以有相同名称的索引");
    println!("   ✅ 同一 schema 内索引名称必须唯一");
    println!("   ✅ 多租户架构下索引重名不会产生冲突");
    
    Ok(())
}
