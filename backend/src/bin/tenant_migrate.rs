use anyhow::Result;
use clap::{Parser, Subcommand};
use dotenvy::dotenv;
use sqlx::postgres::PgPoolOptions;
use std::env;
use tracing::{info, Level};
use deep_mate::service::tenant::sqlx_schema_migration_service::SqlxSchemaMigrationService;

#[derive(Parser)]
#[command(name = "tenant-migrate")]
#[command(about = "Deep-Mate Tenant Schema Migration Tool using SQLx")]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// 显示租户 schema 迁移状态
    Status {
        /// 特定租户 schema 名称
        #[arg(short, long)]
        schema: Option<String>,
    },
    /// 执行租户 schema 迁移
    Migrate {
        /// 特定租户 schema 名称
        #[arg(short, long)]
        schema: Option<String>,
        /// 是否执行所有租户的迁移
        #[arg(long)]
        all: bool,
    },
    /// 创建新的租户迁移文件
    Create {
        /// 迁移描述
        #[arg(short, long)]
        description: String,
    },
    /// 验证迁移文件
    Validate,
}

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_target(true)
        .with_line_number(true)
        .with_max_level(Level::INFO)
        .compact()
        .init();

    // 加载环境变量
    dotenv().ok();

    let cli = Cli::parse();

    // 初始化数据库连接
    let db_url = env::var("DATABASE_URL")?;
    let pool = PgPoolOptions::new()
        .max_connections(5)
        .connect(&db_url)
        .await?;

    match cli.command {
        Commands::Status { schema } => {
            handle_status(&pool, schema).await?;
        }
        Commands::Migrate { schema, all } => {
            handle_migrate(&pool, schema, all).await?;
        }
        Commands::Create { description } => {
            handle_create(&description).await?;
        }
        Commands::Validate => {
            handle_validate().await?;
        }
    }

    Ok(())
}

async fn handle_status(
    pool: &sqlx::PgPool,
    schema: Option<String>,
) -> Result<()> {
    info!("📊 Checking tenant schema migration status...");

    let migration_service = SqlxSchemaMigrationService::new(
        pool.clone(), 
        "migrations_tenant"
    ).await?;

    if let Some(schema_name) = schema {
        // 显示特定 schema 的状态
        let status = migration_service.get_migration_status(&schema_name).await?;
        
        println!("Schema: {}", status.schema_name);
        println!("Applied Migrations: {}", status.applied_count);
        println!("Pending Migrations: {}", status.pending_count);
        
        if !status.applied_migrations.is_empty() {
            println!("\nApplied migrations:");
            for migration in status.applied_migrations {
                println!("  ✅ {} - {} ({})", 
                         migration.version, 
                         migration.description,
                         migration.installed_on.format("%Y-%m-%d %H:%M:%S"));
            }
        }
        
        if !status.pending_migrations.is_empty() {
            println!("\nPending migrations:");
            for migration in status.pending_migrations {
                println!("  ⏳ {} - {}", migration.version, migration.description);
            }
        }
    } else {
        // 显示所有租户 schema 的状态
        let tenant_schemas = get_all_tenant_schemas(pool).await?;
        
        println!("Tenant Schema Migration Status Report");
        println!("=====================================");
        
        for schema_name in tenant_schemas {
            match migration_service.get_migration_status(&schema_name).await {
                Ok(status) => {
                    let status_icon = if status.pending_count > 0 { "⏳" } else { "✅" };
                    println!("{} {:<30} | Applied: {:<3} | Pending: {}", 
                             status_icon, schema_name, status.applied_count, status.pending_count);
                }
                Err(e) => {
                    println!("❌ {:<30} | Error: {}", schema_name, e);
                }
            }
        }
    }

    Ok(())
}

async fn handle_migrate(
    pool: &sqlx::PgPool,
    schema: Option<String>,
    all: bool,
) -> Result<()> {
    let migration_service = SqlxSchemaMigrationService::new(
        pool.clone(), 
        "migrations_tenant"
    ).await?;

    if all {
        info!("🚀 Starting migration for all tenant schemas...");
        migration_service.migrate_all_tenants().await?;
    } else if let Some(schema_name) = schema {
        info!("🚀 Starting migration for schema: {}", schema_name);
        migration_service.migrate_tenant_schema(&schema_name).await?;
    } else {
        return Err(anyhow::anyhow!("Please specify --schema <name> or --all"));
    }

    info!("✅ Migration completed successfully!");
    Ok(())
}

async fn handle_create(description: &str) -> Result<()> {
    info!("📝 Creating new tenant migration: {}", description);

    let migration_dir = "migrations_tenant";
    std::fs::create_dir_all(migration_dir)?;

    // 生成时间戳
    let timestamp = chrono::Utc::now().format("%Y%m%d%H%M%S");
    let filename = format!("{}/{}_{}.sql", 
                          migration_dir, 
                          timestamp,
                          description.replace(' ', "_").to_lowercase());

    let template = format!(
        r#"-- Migration: {}
-- Description: {}
-- Created: {}

-- Add your tenant schema migration SQL here
-- Note: This migration will be applied to each tenant schema individually
-- Do NOT use schema prefixes - the migration system handles schema context

-- Example:
-- CREATE TABLE new_table (
--     id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
--     name VARCHAR(100) NOT NULL,
--     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
-- );

-- ALTER TABLE existing_table ADD COLUMN new_field VARCHAR(50);

-- CREATE INDEX idx_new_table_name ON new_table (name);
"#,
        description,
        description,
        chrono::Utc::now().format("%Y-%m-%d %H:%M:%S UTC")
    );

    std::fs::write(&filename, template)?;
    info!("✅ Migration file created: {}", filename);
    info!("💡 Edit the file and add your SQL statements, then run 'tenant-migrate migrate --all' to apply");

    Ok(())
}

async fn handle_validate() -> Result<()> {
    info!("🔍 Validating tenant migration files...");

    let migration_dir = std::path::Path::new("migrations_tenant");
    if !migration_dir.exists() {
        return Err(anyhow::anyhow!("Migration directory 'migrations_tenant' does not exist"));
    }

    // 使用 SQLx Migrator 来验证迁移文件
    match sqlx::migrate::Migrator::new(migration_dir).await {
        Ok(migrator) => {
            let migrations: Vec<_> = migrator.iter().collect();
            info!("✅ Found {} valid migration files:", migrations.len());
            
            for migration in migrations {
                println!("  📄 {} - {}", migration.version, migration.description);
            }
        }
        Err(e) => {
            return Err(anyhow::anyhow!("Migration validation failed: {}", e));
        }
    }

    info!("✅ All migration files are valid!");
    Ok(())
}

// 辅助函数：获取所有租户 schema
async fn get_all_tenant_schemas(pool: &sqlx::PgPool) -> Result<Vec<String>> {
    let schemas = sqlx::query_scalar!(
        r#"
        SELECT schema_name 
        FROM public.tenants 
        WHERE is_active = true
        ORDER BY schema_name
        "#
    )
    .fetch_all(pool)
    .await?;
    
    Ok(schemas)
}
