-- Migration: Teacher Management System
-- Description: Create teacher information and employment management tables
-- Created: 2025-01-16

-- 教师信息表
CREATE TABLE IF NOT EXISTS teachers (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id           UUID,
    employee_id       VARCHAR(50) NOT NULL,
    teacher_name      VARCHAR(100) NOT NULL,
    phone             VARCHAR(20) CHECK (phone IS NULL OR phone ~ '^[0-9+\-\s()]+$'),
    email             VARCHAR(100) CHECK (email IS NULL OR email ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
    gender            VARCHAR(10) CHECK (gender IN ('男', '女', '未知')),
    date_of_birth     TIMESTAMP WITH TIME ZONE,
    id_card_number    VARCHAR(20) CHECK (id_card_number IS NULL OR length(id_card_number) IN (15, 18)),
    highest_education VARCHAR(50),
    graduation_school VARCHAR(100),
    major             VARCHAR(100),
    hire_date         DATE,
    employment_status VARCHAR(20) DEFAULT '在职' NOT NULL
        CHECK (employment_status IN ('在职', '离职', '退休', '停职', '试用期')),
    title             VARCHAR(50),
    teaching_subjects TEXT[],
    homeroom_class_id BIGINT,
    grade_level_id    INTEGER,
    subject_group_id  BIGINT,
    office_location   VARCHAR(100),
    bio               TEXT,
    is_active         BOOLEAN DEFAULT TRUE NOT NULL,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 教师信息索引
CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers (user_id);
CREATE INDEX IF NOT EXISTS idx_teachers_employee_id ON teachers (employee_id);
CREATE INDEX IF NOT EXISTS idx_teachers_name ON teachers (teacher_name);
CREATE INDEX IF NOT EXISTS idx_teachers_employment_status ON teachers (employment_status);
CREATE INDEX IF NOT EXISTS idx_teachers_is_active ON teachers (is_active);
CREATE INDEX IF NOT EXISTS idx_teachers_teaching_subjects ON teachers USING gin (teaching_subjects);
CREATE INDEX IF NOT EXISTS idx_teachers_homeroom_class_id ON teachers (homeroom_class_id);
CREATE INDEX IF NOT EXISTS idx_teachers_grade_level_id ON teachers (grade_level_id);
CREATE INDEX IF NOT EXISTS idx_teachers_subject_group_id ON teachers (subject_group_id);

-- 表注释
COMMENT ON TABLE teachers IS '教师信息表';
COMMENT ON COLUMN teachers.user_id IS '关联的用户账号ID';
COMMENT ON COLUMN teachers.employee_id IS '教师工号';
COMMENT ON COLUMN teachers.teacher_name IS '教师姓名';
COMMENT ON COLUMN teachers.employment_status IS '在职状态';
COMMENT ON COLUMN teachers.teaching_subjects IS '任教学科列表';
COMMENT ON COLUMN teachers.homeroom_class_id IS '担任班主任的班级ID';
COMMENT ON COLUMN teachers.subject_group_id IS '所属学科组ID';
COMMENT ON COLUMN teachers.office_location IS '办公室位置';
COMMENT ON COLUMN teachers.bio IS '教师简介';
