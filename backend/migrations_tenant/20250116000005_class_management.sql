-- Migration: Class Management System
-- Description: Create administrative and teaching class management tables
-- Created: 2025-01-16

-- 行政班级表
CREATE TABLE IF NOT EXISTS administrative_classes (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_name       VARCHAR(100) NOT NULL,
    code             VARCHAR(50) UNIQUE,
    academic_year    VARCHAR(20),
    grade_level_code VARCHAR(20),
    teacher_id       UUID,
    is_active        BOOLEAN DEFAULT TRUE,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 行政班级索引
CREATE INDEX IF NOT EXISTS idx_administrative_classes_code ON administrative_classes (code);
CREATE INDEX IF NOT EXISTS idx_administrative_classes_grade_level ON administrative_classes (grade_level_code);
CREATE INDEX IF NOT EXISTS idx_administrative_classes_teacher ON administrative_classes (teacher_id);
CREATE INDEX IF NOT EXISTS idx_administrative_classes_active ON administrative_classes (is_active);

-- 教学班级表
CREATE TABLE IF NOT EXISTS teaching_classes (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    class_name       VARCHAR(100) NOT NULL,
    code             VARCHAR(50),
    academic_year    VARCHAR(20),
    subject_group_id UUID,
    teacher_id       UUID,
    is_active        BOOLEAN DEFAULT TRUE,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 教学班级索引
CREATE INDEX IF NOT EXISTS idx_teaching_classes_code ON teaching_classes (code);
CREATE INDEX IF NOT EXISTS idx_teaching_classes_subject_group ON teaching_classes (subject_group_id);
CREATE INDEX IF NOT EXISTS idx_teaching_classes_teacher ON teaching_classes (teacher_id);
CREATE INDEX IF NOT EXISTS idx_teaching_classes_active ON teaching_classes (is_active);

-- 学生教学班级关联表
CREATE TABLE IF NOT EXISTS student_teaching_classes (
    id         UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID,
    class_id   UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (student_id, class_id)
);

-- 学生教学班级关联索引
CREATE INDEX IF NOT EXISTS idx_student_teaching_classes_student ON student_teaching_classes (student_id);
CREATE INDEX IF NOT EXISTS idx_student_teaching_classes_class ON student_teaching_classes (class_id);

-- 表注释
COMMENT ON TABLE administrative_classes IS '行政班级表 - 管理学生的行政归属班级';
COMMENT ON COLUMN administrative_classes.class_name IS '班级名称';
COMMENT ON COLUMN administrative_classes.code IS '班级代码，唯一标识';
COMMENT ON COLUMN administrative_classes.academic_year IS '学年';
COMMENT ON COLUMN administrative_classes.grade_level_code IS '年级代码';
COMMENT ON COLUMN administrative_classes.teacher_id IS '班主任教师ID';

COMMENT ON TABLE teaching_classes IS '教学班级表 - 管理按学科分组的教学班级';
COMMENT ON COLUMN teaching_classes.subject_group_id IS '所属学科组ID';
COMMENT ON COLUMN teaching_classes.teacher_id IS '任课教师ID';

COMMENT ON TABLE student_teaching_classes IS '学生教学班级关联表 - 管理学生与教学班级的多对多关系';
COMMENT ON COLUMN student_teaching_classes.student_id IS '学生ID';
COMMENT ON COLUMN student_teaching_classes.class_id IS '教学班级ID';
