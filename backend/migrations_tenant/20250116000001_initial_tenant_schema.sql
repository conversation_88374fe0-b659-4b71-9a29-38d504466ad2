-- Initial tenant schema setup
-- This migration creates the basic tenant-specific tables

-- 学科组表
CREATE TABLE IF NOT EXISTS subject_groups
(
    id             uuid                     default gen_random_uuid() not null primary key,
    group_name     varchar(100)                                       not null,
    subject_code   varchar(50)                                        not null,
    grade_level_code varchar(50),
    description    text,
    leader_user_id uuid,
    created_at     timestamp with time zone default now(),
    updated_at     timestamp with time zone default now(),
    is_active      boolean                  default true              not null
);

CREATE INDEX IF NOT EXISTS idx_subject_groups_subject ON subject_groups (subject_code);
CREATE INDEX IF NOT EXISTS idx_subject_groups_leader ON subject_groups (leader_user_id);
CREATE INDEX IF NOT EXISTS idx_subject_groups_grade_level ON subject_groups (grade_level_code);

-- 学科组成员表
CREATE TABLE IF NOT EXISTS subject_group_members
(
    id                uuid                     default gen_random_uuid() not null primary key,
    subject_group_id  uuid                                               not null,
    teacher_id        uuid                                               not null,
    role_code         VARCHAR(100),
    joined_at         timestamp with time zone default now()            not null,
    is_active         boolean                  default true              not null,
    created_at        timestamp with time zone default now()            not null,
    updated_at        timestamp with time zone default now()            not null,
    
    CONSTRAINT uk_subject_group_members_active
        UNIQUE (subject_group_id, teacher_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

CREATE INDEX IF NOT EXISTS idx_subject_group_members_subject_group_id ON subject_group_members (subject_group_id);
CREATE INDEX IF NOT EXISTS idx_subject_group_members_teacher_id ON subject_group_members (teacher_id);

-- 用户身份表
CREATE TABLE IF NOT EXISTS user_identities
(
    id          uuid                     default gen_random_uuid() not null primary key,
    user_id     uuid                                               not null references public.users,
    role_id     uuid                                               not null references public.roles,
    target_type varchar(30),
    target_id   uuid,
    subject     varchar(50),
    created_at  timestamp with time zone default now(),
    updated_at  timestamp with time zone default now(),
    unique (user_id, role_id, target_id, subject)
);

CREATE INDEX IF NOT EXISTS idx_user_identities_user ON user_identities (user_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_role ON user_identities (role_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_target ON user_identities (target_type, target_id);

-- 学生表
CREATE TABLE IF NOT EXISTS students
(
    id                      uuid                     default gen_random_uuid() not null primary key,
    student_number          varchar(50)                                        not null unique,
    student_name            varchar(100)                                       not null,
    gender                  varchar(10),
    birth_date              date,
    id_number               varchar(20),
    phone                   varchar(20),
    email                   varchar(100),
    address                 text,
    guardian_name           varchar(100),
    guardian_phone          varchar(20),
    guardian_relation       varchar(20),
    administrative_class_id uuid,
    grade_level_id          uuid references public.grade_levels,
    user_id                 uuid references public.users,
    enrollment_date         date,
    status                  varchar(20) default 'active'::character varying
        CHECK (status IN ('active', 'inactive', 'graduated', 'transferred')),
    profile_level           varchar(20)
        CHECK (profile_level IN ('A+', 'A', 'B+', 'B', 'C+', 'C', 'D+', 'D')),
    profile_tags            jsonb,
    notes                   text,
    created_at              timestamp with time zone default now(),
    updated_at              timestamp with time zone default now()
);

CREATE INDEX IF NOT EXISTS idx_students_class ON students (administrative_class_id);
CREATE INDEX IF NOT EXISTS idx_students_grade ON students (grade_level_id);
CREATE INDEX IF NOT EXISTS idx_students_user ON students (user_id);
CREATE INDEX IF NOT EXISTS idx_students_student_number ON students (student_number);
CREATE INDEX IF NOT EXISTS idx_students_status ON students (status);

-- 教师表
CREATE TABLE IF NOT EXISTS teachers
(
    id                uuid                     default gen_random_uuid() not null primary key,
    user_id           uuid,
    employee_id       varchar(50)                                        not null,
    teacher_name      varchar(100)                                       not null,
    phone             varchar(20) CHECK (phone IS NULL OR phone ~ '^[0-9+\-\s()]+$'),
    email             varchar(100) CHECK (email IS NULL OR email ~ '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'),
    gender            varchar(10) CHECK (gender IN ('男', '女', '未知')),
    date_of_birth     timestamp with time zone,
    id_card_number    varchar(20) CHECK (id_card_number IS NULL OR length(id_card_number) IN (15, 18)),
    highest_education varchar(50),
    graduation_school varchar(100),
    major             varchar(100),
    hire_date         date,
    employment_status varchar(20) default '在职'::character varying not null
        CHECK (employment_status IN ('在职', '离职', '退休', '停职', '试用期')),
    title             varchar(50),
    teaching_subjects text[],
    homeroom_class_id bigint,
    grade_level_id    integer,
    subject_group_id  bigint,
    office_location   varchar(100),
    bio               text,
    is_active         boolean default true not null,
    created_at        timestamp with time zone default now(),
    updated_at        timestamp with time zone default now()
);

CREATE INDEX IF NOT EXISTS idx_teachers_user_id ON teachers (user_id);
CREATE INDEX IF NOT EXISTS idx_teachers_employee_id ON teachers (employee_id);
CREATE INDEX IF NOT EXISTS idx_teachers_name ON teachers (teacher_name);
CREATE INDEX IF NOT EXISTS idx_teachers_employment_status ON teachers (employment_status);
CREATE INDEX IF NOT EXISTS idx_teachers_is_active ON teachers (is_active);

-- 行政班级表
CREATE TABLE IF NOT EXISTS administrative_classes
(
    id               uuid                     default gen_random_uuid() not null primary key,
    class_name       varchar(100)                                       not null,
    code             varchar(50) unique,
    academic_year    varchar(20),
    grade_level_code varchar(20),
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    teacher_id       uuid,
    is_active        boolean                  default true
);

-- 教学班级表
CREATE TABLE IF NOT EXISTS teaching_classes
(
    id               uuid                     default gen_random_uuid() not null primary key,
    class_name       varchar(100)                                       not null,
    code             varchar(50),
    academic_year    varchar(20),
    subject_group_id uuid,
    teacher_id       uuid,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    is_active        boolean                  default true
);

CREATE INDEX IF NOT EXISTS idx_teaching_classes_code ON teaching_classes (code);
CREATE INDEX IF NOT EXISTS idx_teaching_classes_subject_group ON teaching_classes (subject_group_id);
CREATE INDEX IF NOT EXISTS idx_teaching_classes_teacher ON teaching_classes (teacher_id);

-- 学生教学班级关联表
CREATE TABLE IF NOT EXISTS student_teaching_classes
(
    id         uuid                     default gen_random_uuid() not null primary key,
    student_id uuid,
    class_id   uuid,
    created_at timestamp with time zone default now(),
    updated_at timestamp with time zone default now(),
    unique (student_id, class_id)
);

CREATE INDEX IF NOT EXISTS idx_student_teaching_classes_student ON student_teaching_classes (student_id);
CREATE INDEX IF NOT EXISTS idx_student_teaching_classes_class ON student_teaching_classes (class_id);
