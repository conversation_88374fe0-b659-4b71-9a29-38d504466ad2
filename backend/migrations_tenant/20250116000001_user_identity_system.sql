-- Migration: User Identity and Permission System
-- Description: Create user identity and role management tables for tenant schema
-- Created: 2025-01-16

-- 用户身份表 - 管理用户在租户内的身份和权限
CREATE TABLE IF NOT EXISTS user_identities (
    id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id     UUID NOT NULL REFERENCES public.users,
    role_id     UUID NOT NULL REFERENCES public.roles,
    target_type VARCHAR(30),
    target_id   UUID,
    subject     VARCHAR(50),
    created_at  TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at  TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (user_id, role_id, target_id, subject)
);

-- 用户身份索引
CREATE INDEX IF NOT EXISTS idx_user_identities_user ON user_identities (user_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_role ON user_identities (role_id);
CREATE INDEX IF NOT EXISTS idx_user_identities_target ON user_identities (target_type, target_id);

-- 表注释
COMMENT ON TABLE user_identities IS '用户身份表 - 管理用户在租户内的身份和权限关系';
COMMENT ON COLUMN user_identities.user_id IS '用户ID，引用public.users表';
COMMENT ON COLUMN user_identities.role_id IS '角色ID，引用public.roles表';
COMMENT ON COLUMN user_identities.target_type IS '目标类型（如class, subject等）';
COMMENT ON COLUMN user_identities.target_id IS '目标ID';
COMMENT ON COLUMN user_identities.subject IS '学科代码';
