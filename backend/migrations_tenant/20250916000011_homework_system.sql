-- Migration: Homework Management System
-- Description: Create homework, papers, and student assignment tables
-- Created: 2025-01-16

-- 作业表
CREATE TABLE IF NOT EXISTS homework (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_name    VARCHAR(128) NOT NULL,
    homework_status  VARCHAR(64) DEFAULT 'Draft' NOT NULL,
    description      VARCHAR(1024),
    leaf_count       INT4 DEFAULT 0 NOT NULL,
    leaf_total       INT4 DEFAULT 0 NOT NULL,
    page_count       INT4 DEFAULT 0 NOT NULL,
    page_total       INT4 DEFAULT 0 NOT NULL,
    due_date         TIMESTAMP WITH TIME ZONE,
    auto_grade       BOOLEAN DEFAULT FALSE,
    max_attempts     INTEGER DEFAULT 1,
    allow_late_submission BOOLEAN DEFAULT TRUE,
    subject_group_id UUID,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 作业索引
CREATE INDEX IF NOT EXISTS idx_homework_status ON homework (homework_status);
CREATE INDEX IF NOT EXISTS idx_homework_subject_group ON homework (subject_group_id);
CREATE INDEX IF NOT EXISTS idx_homework_due_date ON homework (due_date);

-- 试卷表
CREATE TABLE IF NOT EXISTS papers (
    id           UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paper_name   VARCHAR(250) NOT NULL,
    paper_content JSONB,
    created_at   TIMESTAMPTZ(6) DEFAULT NOW(),
    updated_at   TIMESTAMPTZ(6) DEFAULT NOW()
);

-- 作业试卷关联表
CREATE TABLE IF NOT EXISTS homework_papers (
    id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_id UUID NOT NULL,
    paper_id    UUID NOT NULL
);

-- 作业试卷关联索引
CREATE INDEX IF NOT EXISTS idx_homework_papers_homework ON homework_papers (homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_papers_paper ON homework_papers (paper_id);

-- 作业学科表
CREATE TABLE IF NOT EXISTS homework_subjects (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_id       UUID REFERENCES homework ON DELETE CASCADE,
    subject_id        UUID REFERENCES public.subjects,
    paper_template_id UUID REFERENCES public.exam_papers,
    total_score       NUMERIC(5, 2),
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 作业学科索引
CREATE INDEX IF NOT EXISTS idx_homework_subjects_homework ON homework_subjects (homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_subjects_subject ON homework_subjects (subject_id);

-- 作业学生表
CREATE TABLE IF NOT EXISTS homework_students (
    id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_id   UUID REFERENCES homework ON DELETE CASCADE,
    student_id    UUID REFERENCES students ON DELETE CASCADE,
    scores        NUMERIC(10,2),
    status        VARCHAR DEFAULT 'Unsubmitted',
    class_id      UUID NOT NULL,
    submitted_at  TIMESTAMP WITH TIME ZONE,
    attempt_count INTEGER DEFAULT 0,
    is_late       BOOLEAN DEFAULT FALSE,
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 作业学生索引
CREATE INDEX IF NOT EXISTS idx_homework_students_homework ON homework_students (homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_students_student ON homework_students (student_id);
CREATE INDEX IF NOT EXISTS idx_homework_students_status ON homework_students (status);
CREATE UNIQUE INDEX homework_students_homework_id_idx ON homework_students (homework_id, student_id);

-- 作业反馈表
CREATE TABLE IF NOT EXISTS homework_feedback (
    id          UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_id UUID NOT NULL,
    student_id  UUID NOT NULL,
    score_id    UUID,
    text        TEXT NOT NULL,
    status      VARCHAR(20) NOT NULL
);

-- 作业反馈索引
CREATE INDEX IF NOT EXISTS idx_homework_feedback_homework ON homework_feedback (homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_feedback_student ON homework_feedback (student_id);

-- 作业提交历史表
CREATE TABLE IF NOT EXISTS homework_submission_history (
    id                  UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_student_id UUID NOT NULL REFERENCES homework_students(id) ON DELETE CASCADE,
    attempt_number      INTEGER NOT NULL,
    submission_content  JSONB,
    score               NUMERIC(10,2),
    submitted_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    graded_at           TIMESTAMP WITH TIME ZONE,
    graded_by           UUID REFERENCES teachers(id),
    feedback            TEXT,
    
    CONSTRAINT unique_homework_student_attempt 
        UNIQUE (homework_student_id, attempt_number)
);

-- 作业提交历史索引
CREATE INDEX IF NOT EXISTS idx_homework_submission_history_student ON homework_submission_history (homework_student_id);
CREATE INDEX IF NOT EXISTS idx_homework_submission_history_attempt ON homework_submission_history (attempt_number);

-- 表注释
COMMENT ON TABLE homework IS '作业表 - 管理作业基本信息';
COMMENT ON COLUMN homework.homework_status IS '作业状态：Draft-草稿，Published-已发布，Completed-已完成';
COMMENT ON COLUMN homework.max_attempts IS '最大尝试次数';
COMMENT ON COLUMN homework.allow_late_submission IS '是否允许迟交';

COMMENT ON TABLE papers IS '试卷表 - 存储试卷内容';
COMMENT ON COLUMN papers.paper_content IS '试卷内容（JSON格式）';

COMMENT ON TABLE homework_papers IS '作业试卷关联表 - 管理作业与试卷的关联关系';

COMMENT ON TABLE homework_subjects IS '作业学科表 - 管理作业涉及的学科';

COMMENT ON TABLE homework_students IS '作业学生表 - 管理学生作业完成情况';
COMMENT ON COLUMN homework_students.attempt_count IS '尝试次数';
COMMENT ON COLUMN homework_students.is_late IS '是否迟交';

COMMENT ON TABLE homework_feedback IS '作业反馈表 - 存储作业反馈信息';

COMMENT ON TABLE homework_submission_history IS '作业提交历史表 - 记录学生作业提交的历史版本';
