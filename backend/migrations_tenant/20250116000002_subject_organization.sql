-- Migration: Subject Organization System
-- Description: Create subject groups and member management tables
-- Created: 2025-01-16

-- 学科组表
CREATE TABLE IF NOT EXISTS subject_groups (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_name       VARCHAR(100) NOT NULL,
    subject_code     VARCHAR(50) NOT NULL,
    grade_level_code VARCHAR(50),
    description      TEXT,
    leader_user_id   UUID,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active        BOOLEAN DEFAULT TRUE NOT NULL
);

-- 学科组索引
CREATE INDEX IF NOT EXISTS idx_subject_groups_subject ON subject_groups (subject_code);
CREATE INDEX IF NOT EXISTS idx_subject_groups_leader ON subject_groups (leader_user_id);
CREATE INDEX IF NOT EXISTS idx_subject_groups_grade_level ON subject_groups (grade_level_code);
CREATE INDEX IF NOT EXISTS idx_subject_groups_subject_grade ON subject_groups (subject_code, grade_level_code);

-- 学科组成员表
CREATE TABLE IF NOT EXISTS subject_group_members (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    subject_group_id  UUID NOT NULL,
    teacher_id        UUID NOT NULL,
    role_code         VARCHAR(100),
    joined_at         TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    is_active         BOOLEAN DEFAULT TRUE NOT NULL,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    
    -- 唯一约束：同一教师在同一学科组中只能有一个活跃记录
    CONSTRAINT uk_subject_group_members_active
        UNIQUE (subject_group_id, teacher_id, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- 学科组成员索引
CREATE INDEX IF NOT EXISTS idx_subject_group_members_subject_group_id ON subject_group_members (subject_group_id);
CREATE INDEX IF NOT EXISTS idx_subject_group_members_teacher_id ON subject_group_members (teacher_id);
CREATE INDEX IF NOT EXISTS idx_subject_group_members_is_active ON subject_group_members (is_active);

-- 表注释
COMMENT ON TABLE subject_groups IS '学科组表 - 管理学科教学组织';
COMMENT ON COLUMN subject_groups.group_name IS '学科组名称';
COMMENT ON COLUMN subject_groups.subject_code IS '学科代码';
COMMENT ON COLUMN subject_groups.grade_level_code IS '年级代码';
COMMENT ON COLUMN subject_groups.leader_user_id IS '学科组长用户ID';

COMMENT ON TABLE subject_group_members IS '学科组成员表 - 管理教师与学科组的关系';
COMMENT ON COLUMN subject_group_members.subject_group_id IS '学科组ID';
COMMENT ON COLUMN subject_group_members.teacher_id IS '教师ID';
COMMENT ON COLUMN subject_group_members.role_code IS '在学科组中的角色代码';
