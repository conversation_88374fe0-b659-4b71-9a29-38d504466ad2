-- Migration: Paper Scanning and Processing System
-- Description: Create paper scan, pages, and block processing tables
-- Created: 2025-01-16

-- 试卷扫描表
CREATE TABLE IF NOT EXISTS paper_scans (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id          UUID,
    student_id       UUID,
    student_number   VARCHAR(20),
    scan_method      VARCHAR(20),
    scan_device      VARCHAR(100),
    status           VARCHAR(20) DEFAULT 'unprocessed',
    result           JSONB,
    duplicate_num    INT4 DEFAULT 0,
    blank_num        INT4 DEFAULT 0,
    is_abnormal      BOOLEAN DEFAULT FALSE,
    abnormal_reason  TEXT,
    needs_review     BOOLEAN DEFAULT FALSE,
    reviewed_by      <PERSON><PERSON><PERSON>,
    reviewed_at      TIMESTAMPTZ(6),
    created_at       TIMESTAMPTZ(6) DEFAULT NOW(),
    updated_at       TIMESTAMPTZ(6) DEFAULT NOW(),
    exam_type        VARCHAR NOT NULL,
    batch_no         VARCHAR
);

-- 试卷扫描索引
CREATE INDEX IF NOT EXISTS idx_paper_scans_abnormal ON paper_scans (is_abnormal);
CREATE INDEX IF NOT EXISTS idx_paper_scans_exam ON paper_scans (exam_id);
CREATE INDEX IF NOT EXISTS idx_paper_scans_needs_review ON paper_scans (needs_review);
CREATE INDEX IF NOT EXISTS idx_paper_scans_student ON paper_scans (student_id);
CREATE INDEX IF NOT EXISTS idx_paper_scans_status ON paper_scans (status);
CREATE INDEX IF NOT EXISTS idx_paper_scans_batch ON paper_scans (batch_no);

-- 试卷扫描页面表
CREATE TABLE IF NOT EXISTS paper_scan_pages (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    paper_scan_id    UUID,
    page_num         INTEGER NOT NULL,
    file_name        VARCHAR(200),
    file_url         TEXT NOT NULL,
    rectify_url      TEXT,
    minio_bucket     VARCHAR(100),
    minio_object_key VARCHAR(500),
    file_size        BIGINT,
    scan_quality     INTEGER CHECK (scan_quality BETWEEN 1 AND 10),
    is_duplicate     BOOLEAN DEFAULT FALSE,
    is_blank         BOOLEAN DEFAULT FALSE,
    is_abnormal      BOOLEAN DEFAULT FALSE,
    abnormal_reason  TEXT,
    result           JSONB,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 试卷扫描页面索引
CREATE INDEX IF NOT EXISTS idx_paper_scan_pages_scan ON paper_scan_pages (paper_scan_id);
CREATE INDEX IF NOT EXISTS idx_paper_scan_pages_abnormal ON paper_scan_pages (is_abnormal);
CREATE INDEX IF NOT EXISTS idx_paper_scan_pages_blank ON paper_scan_pages (is_blank);
CREATE INDEX IF NOT EXISTS idx_paper_scan_pages_duplicate ON paper_scan_pages (is_duplicate);
CREATE INDEX IF NOT EXISTS idx_paper_scan_pages_quality ON paper_scan_pages (scan_quality);

-- 试卷扫描块表
CREATE TABLE IF NOT EXISTS paper_scan_blocks (
    id                      UUID PRIMARY KEY,
    paper_scan_page_id      UUID NOT NULL,
    score                   NUMERIC(10,2) DEFAULT 0,
    answer_block_url        VARCHAR,
    answer_content          TEXT,
    status                  VARCHAR,
    abnormal_reason         TEXT,
    answer_block_group_id   UUID,
    serial_number           INT4 NOT NULL DEFAULT 0,
    ocr_confidence          NUMERIC(3,2),
    processing_time         INTEGER,
    needs_manual_review     BOOLEAN DEFAULT FALSE,
    reviewed_by             UUID,
    reviewed_at             TIMESTAMP WITH TIME ZONE,
    created_at              TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at              TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 试卷扫描块索引
CREATE INDEX IF NOT EXISTS idx_paper_scan_blocks_page ON paper_scan_blocks (paper_scan_page_id);
CREATE INDEX IF NOT EXISTS idx_paper_scan_blocks_group ON paper_scan_blocks (answer_block_group_id);
CREATE INDEX IF NOT EXISTS idx_paper_scan_blocks_status ON paper_scan_blocks (status);
CREATE INDEX IF NOT EXISTS idx_paper_scan_blocks_review ON paper_scan_blocks (needs_manual_review);

-- 扫描批次表
CREATE TABLE IF NOT EXISTS scan_batches (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    batch_no          VARCHAR(50) NOT NULL UNIQUE,
    exam_id           UUID,
    batch_name        VARCHAR(200),
    scan_device       VARCHAR(100),
    operator_id       UUID REFERENCES public.users,
    total_papers      INTEGER DEFAULT 0,
    processed_papers  INTEGER DEFAULT 0,
    failed_papers     INTEGER DEFAULT 0,
    batch_status      VARCHAR(20) DEFAULT 'processing' 
        CHECK (batch_status IN ('processing', 'completed', 'failed', 'cancelled')),
    started_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at      TIMESTAMP WITH TIME ZONE,
    processing_notes  TEXT,
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 扫描批次索引
CREATE INDEX IF NOT EXISTS idx_scan_batches_exam ON scan_batches (exam_id);
CREATE INDEX IF NOT EXISTS idx_scan_batches_operator ON scan_batches (operator_id);
CREATE INDEX IF NOT EXISTS idx_scan_batches_status ON scan_batches (batch_status);
CREATE INDEX IF NOT EXISTS idx_scan_batches_batch_no ON scan_batches (batch_no);

-- 表注释
COMMENT ON TABLE paper_scans IS '试卷扫描表 - 记录试卷扫描的基本信息';
COMMENT ON COLUMN paper_scans.exam_type IS '考试类型';
COMMENT ON COLUMN paper_scans.batch_no IS '批次号';
COMMENT ON COLUMN paper_scans.scan_method IS '扫描方式：manual-手动，auto-自动';
COMMENT ON COLUMN paper_scans.status IS '处理状态：unprocessed-未处理，processing-处理中，completed-已完成，failed-失败';

COMMENT ON TABLE paper_scan_pages IS '试卷扫描页面表 - 记录试卷每页的扫描信息';
COMMENT ON COLUMN paper_scan_pages.scan_quality IS '扫描质量评分（1-10）';
COMMENT ON COLUMN paper_scan_pages.minio_bucket IS 'MinIO存储桶名称';
COMMENT ON COLUMN paper_scan_pages.minio_object_key IS 'MinIO对象键';

COMMENT ON TABLE paper_scan_blocks IS '试卷扫描块表 - 记录试卷答题区域的识别结果';
COMMENT ON COLUMN paper_scan_blocks.answer_content IS 'OCR识别的答案内容';
COMMENT ON COLUMN paper_scan_blocks.ocr_confidence IS 'OCR识别置信度';
COMMENT ON COLUMN paper_scan_blocks.needs_manual_review IS '是否需要人工审核';

COMMENT ON TABLE scan_batches IS '扫描批次表 - 管理试卷扫描的批次信息';
COMMENT ON COLUMN scan_batches.batch_no IS '批次编号，唯一标识';
COMMENT ON COLUMN scan_batches.operator_id IS '操作员用户ID';
