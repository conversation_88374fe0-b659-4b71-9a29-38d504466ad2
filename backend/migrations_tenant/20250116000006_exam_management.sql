-- Migration: Exam Management System
-- Description: Create exam, exam subjects, and exam students management tables
-- Created: 2025-01-16

-- 考试表
CREATE TABLE IF NOT EXISTS exams (
    id                       UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name                     VARCHAR(100) NOT NULL,
    type                     VARCHAR(20) CHECK (type IN ('single', 'joint')),
    grade_level              VARCHAR(50) NOT NULL,
    exam_nature              VARCHAR(20) CHECK (exam_nature IN ('formal', 'mock', 'practice')),
    description              TEXT,
    start_time               TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time                 TIMESTAMP WITH TIME ZONE NOT NULL,
    expected_collection_time TIMESTAMP WITH TIME ZONE,
    scan_start_time          TIMESTAMP WITH TIME ZONE,
    grading_mode             VARCHAR(20) CHECK (grading_mode IN ('intelligent', 'manual_then_scan')),
    quality_control          VARCHAR(20) CHECK (quality_control IN ('single', 'double_blind')),
    ai_confidence_threshold  NUMERIC(3, 2),
    manual_review_ratio      NUMERIC(3, 2),
    status                   VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'in_progress', 'completed')),
    created_by               UUID REFERENCES public.users,
    created_at               TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at               TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 考试索引
CREATE INDEX IF NOT EXISTS idx_exams_type ON exams (type);
CREATE INDEX IF NOT EXISTS idx_exams_status ON exams (status);
CREATE INDEX IF NOT EXISTS idx_exams_date ON exams (start_time);
CREATE INDEX IF NOT EXISTS idx_exams_creator ON exams (created_by);
CREATE INDEX IF NOT EXISTS idx_exams_grade_level ON exams (grade_level);

-- 考试学科表
CREATE TABLE IF NOT EXISTS exam_subjects (
    id                UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id           UUID REFERENCES exams ON DELETE CASCADE,
    subject_id        UUID REFERENCES public.subjects,
    paper_template_id UUID REFERENCES public.exam_papers,
    total_score       NUMERIC(5, 2),
    pass_score        NUMERIC(5, 2),
    created_at        TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 考试学科索引
CREATE INDEX IF NOT EXISTS idx_exam_subjects_exam ON exam_subjects (exam_id);
CREATE INDEX IF NOT EXISTS idx_exam_subjects_subject ON exam_subjects (subject_id);

-- 考试学生表
CREATE TABLE IF NOT EXISTS exam_students (
    id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id       UUID REFERENCES exams ON DELETE CASCADE,
    student_id    UUID REFERENCES students ON DELETE CASCADE,
    class_id      UUID,
    seat_number   VARCHAR(20),
    is_absent     BOOLEAN DEFAULT FALSE,
    absent_reason TEXT,
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 考试学生索引
CREATE INDEX IF NOT EXISTS idx_exam_students_exam ON exam_students (exam_id);
CREATE INDEX IF NOT EXISTS idx_exam_students_student ON exam_students (student_id);
CREATE INDEX IF NOT EXISTS idx_exam_students_class ON exam_students (class_id);

-- 联考表
CREATE TABLE IF NOT EXISTS joint_exams (
    id                    UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    main_exam_id          UUID REFERENCES exams ON DELETE CASCADE,
    organizer_tenant_id   UUID REFERENCES public.tenants,
    participant_tenant_id UUID REFERENCES public.tenants,
    invitation_status     VARCHAR(20) CHECK (invitation_status IN ('pending', 'accepted', 'rejected')),
    invitation_sent_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at          TIMESTAMP WITH TIME ZONE,
    sync_status           VARCHAR(20) CHECK (sync_status IN ('pending', 'synced', 'failed')),
    created_at            TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 联考索引
CREATE INDEX IF NOT EXISTS idx_joint_exams_main_exam ON joint_exams (main_exam_id);
CREATE INDEX IF NOT EXISTS idx_joint_exams_organizer ON joint_exams (organizer_tenant_id);
CREATE INDEX IF NOT EXISTS idx_joint_exams_participant ON joint_exams (participant_tenant_id);

-- 表注释
COMMENT ON TABLE exams IS '考试表 - 管理考试基本信息';
COMMENT ON COLUMN exams.type IS '考试类型：single-单独考试，joint-联考';
COMMENT ON COLUMN exams.exam_nature IS '考试性质：formal-正式考试，mock-模拟考试，practice-练习';
COMMENT ON COLUMN exams.grading_mode IS '阅卷模式：intelligent-智能阅卷，manual_then_scan-先人工后扫描';
COMMENT ON COLUMN exams.quality_control IS '质量控制：single-单评，double_blind-双盲评阅';

COMMENT ON TABLE exam_subjects IS '考试学科表 - 管理考试包含的学科';
COMMENT ON TABLE exam_students IS '考试学生表 - 管理参加考试的学生';
COMMENT ON TABLE joint_exams IS '联考表 - 管理多租户联合考试';
