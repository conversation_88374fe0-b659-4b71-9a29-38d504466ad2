-- Migration: Answer Card and Block System
-- Description: Create answer card blocks and question linking tables
-- Created: 2025-01-16

-- 答题卡题块表
CREATE TABLE IF NOT EXISTS answer_card_blocks (
    id               UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id          UUID REFERENCES exams ON DELETE CASCADE,
    block_name       VARCHAR(100) NOT NULL,
    block_type       VARCHAR(30) CHECK (block_type IN ('single_question', 'multi_question', 'composite')),
    position_info    JSONB NOT NULL,
    template_version VARCHAR(50),
    max_score        NUMERIC(5, 2),
    is_active        BOOLEAN DEFAULT TRUE,
    created_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at       TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 答题卡题块索引
CREATE INDEX IF NOT EXISTS idx_answer_card_blocks_exam ON answer_card_blocks (exam_id);
CREATE INDEX IF NOT EXISTS idx_answer_card_blocks_type ON answer_card_blocks (block_type);
CREATE INDEX IF NOT EXISTS idx_answer_card_blocks_active ON answer_card_blocks (is_active);

-- 题块问题关联表
CREATE TABLE IF NOT EXISTS card_block_question_links (
    id            UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    card_block_id UUID REFERENCES answer_card_blocks ON DELETE CASCADE,
    question_id   UUID NOT NULL,
    link_type     VARCHAR(20) CHECK (link_type IN ('one_to_one', 'one_to_many', 'many_to_one', 'many_to_many')),
    weight_ratio  NUMERIC(3, 2) DEFAULT 1.0,
    score_mapping JSONB,
    is_primary    BOOLEAN DEFAULT TRUE,
    created_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at    TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (card_block_id, question_id)
);

-- 题块问题关联索引
CREATE INDEX IF NOT EXISTS idx_card_block_question_links_block ON card_block_question_links (card_block_id);
CREATE INDEX IF NOT EXISTS idx_card_block_question_links_question ON card_block_question_links (question_id);
CREATE INDEX IF NOT EXISTS idx_card_block_question_links_type ON card_block_question_links (link_type);

-- 评分标准表
CREATE TABLE IF NOT EXISTS answer_block_scoring_criteria (
    id            UUID NOT NULL UNIQUE,
    block_id      UUID NOT NULL,
    serial_number INTEGER,
    scoring_type  VARCHAR NOT NULL,
    config        JSONB,
    is_enable     BOOLEAN DEFAULT FALSE,
    answer        VARCHAR,
    score         INTEGER DEFAULT 0
);

-- 评分标准索引
CREATE INDEX IF NOT EXISTS idx_answer_block_scoring_criteria_block ON answer_block_scoring_criteria (block_id);
CREATE INDEX IF NOT EXISTS idx_answer_block_scoring_criteria_enable ON answer_block_scoring_criteria (is_enable);

-- 分数表
CREATE TABLE IF NOT EXISTS scores (
    id           UUID NOT NULL UNIQUE,
    criteria_id  UUID NOT NULL,
    student_id   UUID,
    score_status VARCHAR,
    score        NUMERIC(10,2)
);

-- 分数详情表
CREATE TABLE IF NOT EXISTS score_details (
    id           UUID NOT NULL,
    score_id     UUID,
    reason       JSONB,
    score        NUMERIC(10,2) DEFAULT 0,
    scoring_type JSONB,
    created_at   TIMESTAMPTZ(6) NOT NULL DEFAULT NOW(),
    ocr          TEXT,
    status       VARCHAR(20)
);

-- 分数块关联表
CREATE TABLE IF NOT EXISTS score_blocks (
    score_id UUID NOT NULL,
    block_id UUID NOT NULL,
    CONSTRAINT score_block_unique UNIQUE (score_id, block_id)
);

-- 表注释
COMMENT ON TABLE answer_card_blocks IS '答题卡题块表 - 管理答题卡的题目区域划分';
COMMENT ON COLUMN answer_card_blocks.block_name IS '题块名称';
COMMENT ON COLUMN answer_card_blocks.block_type IS '题块类型：单题、多题、复合题';
COMMENT ON COLUMN answer_card_blocks.position_info IS '题块位置信息（JSON格式）';

COMMENT ON TABLE card_block_question_links IS '题块问题关联表 - 管理题块与题目的关联关系';
COMMENT ON COLUMN card_block_question_links.link_type IS '关联类型：一对一、一对多等';
COMMENT ON COLUMN card_block_question_links.weight_ratio IS '权重比例';

COMMENT ON TABLE answer_block_scoring_criteria IS '答题块评分标准表';
COMMENT ON COLUMN answer_block_scoring_criteria.scoring_type IS '评分类型';
COMMENT ON COLUMN answer_block_scoring_criteria.is_enable IS '是否启用，只能启用一个';

COMMENT ON TABLE scores IS '分数表 - 存储学生答题得分';
COMMENT ON TABLE score_details IS '分数详情表 - 存储评分详细信息';
COMMENT ON TABLE score_blocks IS '分数块关联表 - 关联分数与题块';
