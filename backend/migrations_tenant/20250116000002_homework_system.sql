-- Homework and assignment system tables

-- 作业表
CREATE TABLE IF NOT EXISTS homework
(
    id               uuid                     default gen_random_uuid() not null primary key,
    homework_name    varchar(128)                                       not null,
    homework_status  varchar(64)              default 'Draft'::character varying not null,
    description      varchar(1024),
    leaf_count       int4                     default 0                 not null,
    leaf_total       int4                     default 0                 not null,
    page_count       int4                     default 0                 not null,
    page_total       int4                     default 0                 not null,
    due_date         timestamp with time zone,
    auto_grade       boolean                  default false,
    max_attempts     integer                  default 1,
    allow_late_submission boolean             default true,
    created_at       timestamp with time zone default now(),
    updated_at       timestamp with time zone default now(),
    subject_group_id uuid
);

COMMENT ON TABLE homework IS '作业表';

-- 作业试卷关联表
CREATE TABLE IF NOT EXISTS homework_papers (
    id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    homework_id uuid NOT NULL,
    paper_id uuid NOT NULL
);

-- 作业学科表
CREATE TABLE IF NOT EXISTS homework_subjects
(
    id                uuid                     default gen_random_uuid() not null primary key,
    homework_id       uuid references homework on delete cascade,
    subject_id        uuid references public.subjects,
    paper_template_id uuid references public.exam_papers,
    total_score       numeric(5, 2),
    created_at        timestamp with time zone default now()
);

CREATE INDEX IF NOT EXISTS idx_homework_subjects_homework ON homework_subjects (homework_id);

-- 作业学生表
CREATE TABLE IF NOT EXISTS homework_students
(
    id          uuid                     default gen_random_uuid() not null primary key,
    homework_id uuid references homework on delete cascade,
    student_id  uuid references students on delete cascade,
    scores      numeric(10,2),
    status      varchar                  default 'Unsubmitted'::character varying,
    class_id    uuid                     not null,
    submitted_at timestamp with time zone,
    attempt_count integer                 default 0,
    is_late     boolean                  default false,
    created_at  timestamp with time zone default now(),
    updated_at  timestamp with time zone default now()
);

CREATE INDEX IF NOT EXISTS idx_homework_students_homework ON homework_students (homework_id);
CREATE INDEX IF NOT EXISTS idx_homework_students_student ON homework_students (student_id);
CREATE UNIQUE INDEX homework_students_homework_id_idx ON homework_students (homework_id, student_id);

-- 作业反馈表
CREATE TABLE IF NOT EXISTS homework_feedback (
    id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    homework_id uuid NOT NULL,
    student_id uuid NOT NULL,
    score_id uuid,
    text text NOT NULL,
    status varchar(20) NOT NULL
);

-- 作业提交历史表
CREATE TABLE IF NOT EXISTS homework_submission_history (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    homework_student_id UUID NOT NULL REFERENCES homework_students(id) ON DELETE CASCADE,
    attempt_number INTEGER NOT NULL,
    submission_content JSONB,
    score NUMERIC(10,2),
    submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    graded_at TIMESTAMP WITH TIME ZONE,
    graded_by UUID REFERENCES teachers(id),
    feedback TEXT,
    
    CONSTRAINT unique_homework_student_attempt 
        UNIQUE (homework_student_id, attempt_number)
);

CREATE INDEX IF NOT EXISTS idx_homework_submission_history_student ON homework_submission_history (homework_student_id);
CREATE INDEX IF NOT EXISTS idx_homework_submission_history_attempt ON homework_submission_history (attempt_number);

-- 试卷表
CREATE TABLE IF NOT EXISTS papers (
    id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
    paper_name varchar(250) NOT NULL,
    paper_content jsonb,
    created_at timestamptz(6) DEFAULT now(),
    updated_at timestamptz(6) DEFAULT now()
);

-- 评分标准表
CREATE TABLE IF NOT EXISTS answer_block_scoring_criteria
(
    id            uuid    not null unique,
    block_id      uuid    not null,
    serial_number integer,
    scoring_type  varchar not null,
    config        jsonb,
    is_enable     boolean default false,
    answer        varchar,
    score         integer default 0
);

COMMENT ON TABLE answer_block_scoring_criteria IS '块关联评分标准';
COMMENT ON COLUMN answer_block_scoring_criteria.id IS '主键';
COMMENT ON COLUMN answer_block_scoring_criteria.block_id IS '题块ID';
COMMENT ON COLUMN answer_block_scoring_criteria.serial_number IS '序号';
COMMENT ON COLUMN answer_block_scoring_criteria.scoring_type IS '评价类型';
COMMENT ON COLUMN answer_block_scoring_criteria.config IS '对应配置评分细则';
COMMENT ON COLUMN answer_block_scoring_criteria.is_enable IS '是否启用,只能启用一个';
COMMENT ON COLUMN answer_block_scoring_criteria.answer IS '作答内容';
COMMENT ON COLUMN answer_block_scoring_criteria.score IS '分值';

-- 分数表
CREATE TABLE scores (
    id uuid NOT NULL,
    criteria_id uuid NOT NULL,
    student_id uuid,
    score_status varchar,
    score numeric(10,2),
    CONSTRAINT score_unique UNIQUE (id)
);

COMMENT ON COLUMN scores.id IS '主键';
COMMENT ON COLUMN scores.criteria_id IS '评分块ID';
COMMENT ON COLUMN scores.student_id IS '学生唯一ID';
COMMENT ON COLUMN scores.score_status IS '评分状态(未分发、已分发OCR、OCR识别失败、)';
COMMENT ON COLUMN scores.score IS '分数';
COMMENT ON TABLE scores IS '块关联评分结果';

-- 分数详情表
CREATE TABLE score_details (
    id uuid NOT NULL,
    score_id uuid,
    reason jsonb,
    score numeric(10,2) DEFAULT 0,
    scoring_type jsonb,
    created_at timestamptz(6) NOT NULL DEFAULT now(),
    ocr text,
    status varchar(20)
);

COMMENT ON COLUMN score_details.id IS '主键ID';
COMMENT ON COLUMN score_details.score_id IS '评分主表ID';
COMMENT ON COLUMN score_details.reason IS '评分理由';
COMMENT ON COLUMN score_details.score IS '分数';
COMMENT ON COLUMN score_details.scoring_type IS '评分类型(AI评阅、人工打分)';
COMMENT ON COLUMN score_details.ocr IS 'ocr结果';
COMMENT ON COLUMN score_details.status IS '状态（评分错误/成功等）';
COMMENT ON TABLE score_details IS '评分理由';

-- 分数块关联表
CREATE TABLE score_blocks (
    score_id uuid NOT NULL,
    block_id uuid NOT NULL,
    CONSTRAINT score_block_unique UNIQUE (score_id, block_id)
);

COMMENT ON CONSTRAINT score_block_unique ON score_blocks IS '分数切块关联主键';
