[package]
name = "deep_mate"
version = "6.2.0"
edition = "2021"

[lib]
name = "deep_mate"
path = "src/lib.rs"

[[bin]]
name = "tenant-migrate"
path = "src/bin/tenant_migrate.rs"

[[bin]]
name = "test-multi-schema-indexes"
path = "src/bin/test_multi_schema_indexes.rs"

[dependencies]
qc_sqlx_derive = {path = "../qc_sqlx_derive"}
tokio = { version = "1", features = ["full"] }
axum = { version = "0.8", features = ["ws", "multipart", "macros"] }
tower-http = { version = "0.6", features = ["cors"] }
sqlx = { version = "0.8", features = ["postgres", "runtime-tokio-rustls", "uuid", "chrono", "json", "bigdecimal", "ipnetwork"] }
dotenvy = "0.15"
uuid = { version = "1.18", features = ["v4","serde"] }
anyhow = "1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
argon2 = "0.5"
rand_core = "0.9"
jsonwebtoken = "9"
chrono = { version = "0.4", features = ["serde"] }            # ✅ 开启 serde 特性
regex = "1"
once_cell = "1.21"
thiserror = "2.0"
tracing = "0.1"
tracing-subscriber = "0.3"
async-trait = "0.1"
rand = "0.9"
ipnetwork = "0.20"
# MinIO and file handling
minio = "0.3"
bytes = "1.10"
mime_guess = "2.0"
zip = "5.1"
tempfile = "3.22"
walkdir = "2.5"
# Casbin RBAC system
casbin = { version = "2.10", default-features = false, features = ["runtime-tokio", "cached","logging", "incremental"] }
# For thread-safe caching in multi-tenant enforcer
dashmap = "6.1"
clap = { version = "4.5", optional = true }
reqwest = { version = "0.12", features = ["json", "stream"] }  # 启用 JSON 和流式传输
futures = "0.3"
redis = { version = "0.32", features = ["tokio-comp", "aio"] }
bb8 = "0.9"
bb8-redis = "0.24"
bigdecimal = { version = "0.4", features = ["serde-json"] }
calamine = { version = "0.30", features = ["dates"] }
rust_xlsxwriter = "0.90"
rayon = "1.11"

[dev-dependencies]
dotenvy = "0.15"
reqwest = { version = "0.12", features = ["json", "cookies"] }

[features]
default = ["cli"]
cli = ["clap"]